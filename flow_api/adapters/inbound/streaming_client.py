"""
Streaming client module for flow_api.

This module provides specialized streaming functionality for AI models.
"""

from typing import List, Dict, Any, Optional, Union, Iterator
import json
import requests
import base64
import binascii

from flow_api.core.capabilities import Capability
from flow_api.core.model_registry import ModelRegistry
from flow_api.adapters.inbound.model_factory import ModelFactory
from flow_api.adapters.inbound.capability_factory import CapabilityClientManager
from flow_api.adapters.outbound.api.specialized_client import SpecializedClient
from flow_api.adapters.inbound.response_processors.processor_factory import ResponseProcessorFactory
from flow_api.exceptions.client_error import ClientError
from flow_api.utils.validation import validate_messages
from flow_api.utils.logging import get_logger, log_and_raise


logger = get_logger(__name__)


class StreamingClient:
    """Specialized client for streaming responses from AI models."""

    def __init__(self, model_name: Optional[str] = None):
        """Initialize the streaming client.

        Args:
            model_name: The model to use for streaming. If None, must be set later.
        """
        logger.debug("=========== Starting StreamingClient ===========")
        self._model_registry = ModelRegistry()
        self._current_manager: Optional[CapabilityClientManager] = None
        self._current_client: Optional[SpecializedClient] = None
        self._response_processor = None

        if model_name:
            self.with_model(model_name)

    def with_model(self, model_name: str) -> 'StreamingClient':
        """Set the model to use for streaming.

        Args:
            model_name: The name of the model.

        Returns:
            The client instance for chaining.

        Raises:
            ClientError: If the model does not exist or doesn't support streaming.
        """
        self._current_manager = ModelFactory.create_client_manager(model_name)
        
        # Check if model supports streaming
        if not self._current_manager.model.has_capability(Capability.STREAMING):
            log_and_raise(
                logger,
                f"Model '{model_name}' does not support streaming capability",
                ClientError,
                "error"
            )
        
        # Set up chat client for streaming
        self._current_client = self._current_manager.get_client(Capability.CHAT_CONVERSATION)
        
        # Create response processor for the model
        self._response_processor = ResponseProcessorFactory.create_processor(self._current_manager.model)
        
        return self

    def stream(
        self,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[Union[str, List[Dict[str, Any]]]] = None,
        **kwargs
    ) -> Iterator[str]:
        """Stream responses from the AI model.

        Args:
            system_prompt: The system prompt.
            user_prompt: The user prompt, either as a string or a list of message objects.
            **kwargs: Additional parameters for the API request.

        Yields:
            Content chunks from the streaming response.

        Raises:
            ClientError: If no model is selected or the request fails.
        """
        # Ensure we have a model selected
        if self._current_manager is None:
            log_and_raise(
                logger,
                "No model selected. Use with_model() first.",
                ClientError,
                "debug"
            )

        # Ensure we have a client
        if self._current_client is None:
            log_and_raise(
                logger,
                "No specialized client available. Use with_model() first.",
                ClientError,
                "debug"
            )

        # Process user_prompt to ensure it's in the correct format
        messages = validate_messages(user_prompt)

        # Prepare messages for the API
        api_messages = []
        if system_prompt:
            api_messages.append({"role": "system", "content": system_prompt})
        api_messages.extend(messages)

        # Force streaming mode
        kwargs['stream'] = True

        # Build the request payload using the specialized client
        payload = self._current_client.build_request_payload(
            messages=api_messages,
            **kwargs
        )
        logger.debug(f"Streaming payload: {json.dumps(payload, indent=2)}")

        # Get authentication token
        from flow_api.adapters.outbound.auth.token_manager import TokenManager
        token_manager = TokenManager()
        token = token_manager.get_valid_token()

        # Get endpoint URL - use streaming endpoint for Amazon Bedrock
        from flow_api.config import Config
        config = Config()
        endpoint_path = self._current_client.get_endpoint_path()

        # Check if this is Amazon Bedrock and use streaming endpoint
        if hasattr(self._current_client, 'model') and hasattr(self._current_client.model, 'provider'):
            from flow_api.core.models import ModelProvider
            logger.debug(f"Current client model provider: {self._current_client.model.provider}")
            if self._current_client.model.provider == ModelProvider.AMAZON_BEDROCK:
                endpoint_path = config.get_endpoint_path("amazon_bedrock", "chat_streaming")

        url = f"{config.api_base_url}{endpoint_path}"

        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'FlowTenant': config.flow_tenant,
            'Authorization': f'Bearer {token}',
            'FlowAgent': self._current_client.model.name
        }

        # Make the streaming API request
        try:
            logger.debug(f"Sending streaming request to {url}")
            response = requests.post(url, headers=headers, data=json.dumps(payload), stream=True)
            response.raise_for_status()

            # Process streaming response
            for chunk in self._handle_streaming_response(response):
                yield chunk

        except requests.exceptions.HTTPError as err:
            # Try to extract detailed error information from response
            error_details = f"HTTP error: {err}"
            try:
                if hasattr(err, 'response') and err.response is not None:
                    try:
                        error_response = err.response.json()
                        from flow_api.utils.logging import extract_error_message
                        detailed_message = extract_error_message(error_response)
                        error_details = f"HTTP error: {err.response.status_code} {err.response.reason} for url: {err.response.url} - {detailed_message}"
                        logger.error(f"Full error response: {error_response}")
                    except (ValueError, KeyError):
                        # If JSON parsing fails, use the response text
                        error_text = err.response.text if err.response.text else str(err)
                        error_details = f"HTTP error: {err.response.status_code} {err.response.reason} for url: {err.response.url} - {error_text}"
            except Exception:
                # If anything fails, fall back to original error
                pass
            log_and_raise(logger, error_details, ClientError, "error")
        except requests.exceptions.RequestException as err:
            log_and_raise(logger, f"Request error: {err}", ClientError, "error")
        except Exception as err:
            log_and_raise(logger, f"Unexpected error: {err}", ClientError, "error")

    def _handle_streaming_response(self, response) -> Iterator[str]:
        """Handle streaming response and return an iterator of chunks.

        Args:
            response: The streaming HTTP response.

        Yields:
            Parsed chunks from the streaming response.
        """
        total_content = ""
        line_count = 0
        current_event_data = {}

        logger.debug("Starting to process streaming response...")

        for line in response.iter_lines(decode_unicode=False):
            if line:
                line_count += 1
                try:
                    # Decode line safely
                    line_str = line.decode('utf-8', errors='replace')

                    # Handle AWS Event Stream format for Amazon Bedrock
                    if line_str.startswith(':'):
                        # This is an event stream header line
                        if line_str.startswith(':event-type'):
                            # Extract event type
                            event_type = line_str.split(' ', 1)[1] if ' ' in line_str else 'chunk'
                            current_event_data['event_type'] = event_type.strip()
                            logger.debug(f"Event type: {current_event_data['event_type']}")

                        elif line_str.startswith(':content-type'):
                            # Extract content type
                            content_type = line_str.split(' ', 1)[1] if ' ' in line_str else 'application/json'
                            current_event_data['content_type'] = content_type.strip()

                        elif line_str.startswith(':message-type'):
                            # Check if this line contains both header and data (Amazon Bedrock format)
                            # Format: :message-typeevent{"bytes":"base64data..."}
                            if '{' in line_str:
                                # Extract the JSON part after the message-type header
                                json_start = line_str.find('{')
                                json_part = line_str[json_start:]

                                # Find the end of the JSON object by counting braces
                                brace_count = 0
                                json_end = json_start
                                for i, char in enumerate(json_part):
                                    if char == '{':
                                        brace_count += 1
                                    elif char == '}':
                                        brace_count -= 1
                                        if brace_count == 0:
                                            json_end = json_start + i + 1
                                            break

                                # Extract only the JSON part
                                clean_json = line_str[json_start:json_end]

                                try:
                                    event_data = json.loads(clean_json)

                                    # Process Bedrock event with base64 encoded bytes
                                    if 'bytes' in event_data:
                                        decoded_bytes = base64.b64decode(event_data['bytes'])
                                        chunk_data = json.loads(decoded_bytes.decode('utf-8'))

                                        # Extract content using response processor if available
                                        content = None
                                        if self._response_processor:
                                            content = self._response_processor.process_streaming_chunk(chunk_data)
                                        else:
                                            content = self._extract_legacy_streaming_content(chunk_data)

                                        if content:
                                            total_content += content
                                            yield content

                                except (json.JSONDecodeError, binascii.Error, KeyError) as e:
                                    logger.debug(f"Failed to parse Bedrock combined header+data: {e}")
                                    continue
                            else:
                                # Regular message-type header without data
                                message_type = line_str.split(' ', 1)[1] if ' ' in line_str else 'event'
                                current_event_data['message_type'] = message_type.strip()
                                logger.debug(f"Message type: {current_event_data['message_type']}")
                        continue

                    elif line_str.startswith('data:'):
                        # Extract the data part
                        data_part = line_str[5:].strip()  # Remove 'data:' prefix

                        # Check for end of stream first
                        if data_part == '[DONE]':
                            logger.debug(f"Streaming completed. Total content length: {len(total_content)} characters")
                            break

                        try:
                            # Parse as JSON first
                            event_data = json.loads(data_part)

                            # Use response processor to handle all formats
                            content = None
                            if self._response_processor:
                                content = self._response_processor.process_streaming_chunk(event_data)
                            else:
                                content = self._extract_legacy_streaming_content(event_data)

                            if content:
                                total_content += content
                                yield content

                        except (json.JSONDecodeError, binascii.Error, KeyError) as e:
                            logger.debug(f"Failed to parse streaming data: {e}")
                            continue

                    elif line_str.strip() == '':
                        # Empty line indicates end of event headers, next line should be data
                        continue

                    else:
                        # This might be raw event data (for AWS Event Stream format)
                        # Try to parse as JSON directly
                        try:
                            event_data = json.loads(line_str)

                            # Process Bedrock event with base64 encoded bytes
                            if 'bytes' in event_data:
                                decoded_bytes = base64.b64decode(event_data['bytes'])
                                chunk_data = json.loads(decoded_bytes.decode('utf-8'))

                                # Extract content using response processor if available
                                content = None
                                if self._response_processor:
                                    content = self._response_processor.process_streaming_chunk(chunk_data)
                                else:
                                    content = self._extract_legacy_streaming_content(chunk_data)

                                if content:
                                    total_content += content
                                    yield content

                        except (json.JSONDecodeError, binascii.Error, KeyError) as e:
                            logger.debug(f"Failed to parse raw event data: {e}")
                            continue

                except UnicodeDecodeError:
                    # Skip lines that can't be decoded
                    continue

    def _extract_legacy_streaming_content(self, chunk_data: dict) -> Optional[str]:
        """Legacy method to extract content from streaming chunks.
        
        Args:
            chunk_data: Raw chunk data from streaming response.
            
        Returns:
            Extracted content string, or None if no content.
        """
        # Handle Bedrock formats
        if 'bytes' in chunk_data:
            try:
                decoded_bytes = base64.b64decode(chunk_data['bytes'])
                bedrock_data = json.loads(decoded_bytes.decode('utf-8'))
                
                if 'delta' in bedrock_data and 'text' in bedrock_data['delta']:
                    return bedrock_data['delta']['text']
                elif 'generation' in bedrock_data:
                    return bedrock_data['generation'] if bedrock_data['generation'] else None
                    
            except (json.JSONDecodeError, binascii.Error):
                return None
        
        # Handle OpenAI format
        elif 'choices' in chunk_data and chunk_data['choices']:
            choice = chunk_data['choices'][0]
            if 'delta' in choice and 'content' in choice['delta']:
                return choice['delta']['content']
        
        return None

    def get_current_model(self) -> Optional[str]:
        """Get the name of the currently selected model.

        Returns:
            The name of the currently selected model, or None if no model is selected.
        """
        if self._current_manager:
            return self._current_manager.model.name
        return None

    def supports_streaming(self) -> bool:
        """Check if the current model supports streaming.

        Returns:
            True if the current model supports streaming, False otherwise.
        """
        if self._current_manager and self._current_manager.model:
            return self._current_manager.model.has_capability(Capability.STREAMING)
        return False
