"""
FlowAPIClient module for flow_api.

This module provides the main client interface for the library.
"""

from typing import List, Dict, Any, Optional, Union

from flow_api.core.capabilities import Capability
from flow_api.core.model_registry import ModelRegistry
from flow_api.adapters.inbound.model_factory import ModelFactory
from flow_api.adapters.inbound.capability_factory import CapabilityClientManager
from flow_api.adapters.outbound.api.specialized_client import SpecializedClient
from flow_api.adapters.inbound.response_processors.processor_factory import ResponseProcessorFactory
from flow_api.adapters.inbound.reasoning_response import ReasoningResponse
from flow_api.exceptions.client_error import ClientError
from flow_api.utils.validation import validate_messages
from flow_api.utils.logging import get_logger, log_and_raise


logger = get_logger(__name__)


class FlowAPIClient:
    """Main client interface for flow_api.

    This class provides a high-level interface for interacting with AI models.
    """

    def __init__(self, default_model: Optional[str] = None):
        """Initialize the client.

        Args:
            default_model: The default model to use if none is specified.
        """
        logger.debug("=========== Starting FlowAPIClient  ===========")
        self._model_registry = ModelRegistry()
        self._current_manager: Optional[CapabilityClientManager] = None
        self._current_client: Optional[SpecializedClient] = None
        self._response_processor = None

        # Set the default model if provided
        if default_model:
            self.with_model(default_model)
    
    def with_model(self, model_name: str) -> 'FlowAPIClient':
        """Set the model to use for subsequent API calls.

        Args:
            model_name: The name of the model.

        Returns:
            The client instance for chaining.

        Raises:
            ClientError: If the model does not exist.
        """
        self._current_manager = ModelFactory.create_client_manager(model_name)
        self._current_client = None  # Will be set when a capability is needed

        # Create response processor for the model
        if self._current_manager and self._current_manager.model:
            self._response_processor = ResponseProcessorFactory.create_processor(self._current_manager.model)

        return self
    
    def with_capability(self, capability: Union[str, Capability]) -> 'FlowAPIClient':
        """Set the model to use based on a required capability.

        If multiple models have the capability, the first one is used.

        Args:
            capability: The required capability.

        Returns:
            The client instance for chaining.

        Raises:
            ClientError: If no model with the capability exists.
        """
        # Convert string to Capability enum if needed
        capability_enum: Capability
        if isinstance(capability, str):
            try:
                capability_enum = Capability.from_str(capability)
            except ValueError:
                log_and_raise(logger, f"Invalid capability: {capability}", ClientError, "debug")
        else:
            capability_enum = capability

        # Get models with the capability
        models = self._model_registry.get_models_by_capability(capability_enum)

        if not models:
            log_and_raise(logger, f"No model found with capability '{capability_enum.value}'", ClientError, "debug")

        # Use the first model with the capability
        model = models[0]
        self._current_manager = ModelFactory.create_client_manager_for_model(model)
        self._current_client = self._current_manager.get_client(capability_enum)

        # Create response processor for the model
        self._response_processor = ResponseProcessorFactory.create_processor(model)

        return self
    
    def with_capabilities(self, capabilities: List[Union[str, Capability]]) -> 'FlowAPIClient':
        """Set the model to use based on required capabilities.

        If multiple models have all the capabilities, the first one is used.

        Args:
            capabilities: The required capabilities.

        Returns:
            The client instance for chaining.

        Raises:
            ClientError: If no model with all the capabilities exists.
        """
        # Convert strings to Capability enums if needed
        capability_enums = []
        for cap in capabilities:
            if isinstance(cap, str):
                try:
                    capability_enums.append(Capability.from_str(cap))
                except ValueError:
                    log_and_raise(logger, f"Invalid capability: {cap}", ClientError, "debug")
            else:
                capability_enums.append(cap)

        # Get models with all the capabilities
        models = self._model_registry.get_models_by_capabilities(capability_enums)

        if not models:
            cap_values = [cap.value for cap in capability_enums]
            log_and_raise(logger, f"No model found with all capabilities: {cap_values}", ClientError, "debug")

        # Use the first model with all the capabilities
        model = models[0]
        self._current_manager = ModelFactory.create_client_manager_for_model(model)
        # Use the first capability to get a client (user can switch later if needed)
        self._current_client = self._current_manager.get_client(capability_enums[0])

        # Create response processor for the model
        self._response_processor = ResponseProcessorFactory.create_processor(model)

        return self
    
    def get_current_model(self) -> Optional[str]:
        """Get the name of the currently selected model.

        Returns:
            The name of the currently selected model, or None if no model is selected.
        """
        if self._current_manager:
            return self._current_manager.model.name
        return None
    
    def list_models(self) -> List[Dict[str, Any]]:
        """List all available models.
        
        Returns:
            A list of all available models as dictionaries.
        """
        return [model.to_dict() for model in self._model_registry.get_all_models()]
    
    def list_capabilities(self) -> List[str]:
        """List all available capabilities.

        Returns:
            A list of all available capabilities as strings.
        """
        return [cap.value for cap in self._model_registry.get_available_capabilities()]

    def check_connection(self) -> Dict[str, Any]:
        """Check the connection to the API and ensure authentication is working.

        This method allows you to verify the connection before making API calls,
        avoiding delays during actual requests. It will obtain a new authentication
        token if needed and cache it for subsequent use.

        Returns:
            A dictionary containing connection status information:
            - 'status': 'connected' if successful, 'error' if failed
            - 'token_source': 'cache' if using cached token, 'new' if obtained new token
            - 'message': Human-readable status message
            - 'error': Error message if status is 'error' (optional)

        Example:
            >>> client = FlowAPIClient()
            >>> result = client.check_connection()
            >>> if result['status'] == 'connected':
            ...     print(f"✅ {result['message']}")
            ... else:
            ...     print(f"❌ {result['message']}: {result.get('error', '')}")
        """
        from flow_api.adapters.outbound.auth.token_manager import TokenManager

        logger.info("Checking API connection...")
        token_manager = TokenManager()
        result = token_manager.check_connection()

        # Log the result for user visibility
        if result['status'] == 'connected':
            logger.info(f"✅ Connection check successful: {result['message']}")
        else:
            logger.error(f"❌ Connection check failed: {result['message']}")
            if 'error' in result:
                logger.error(f"Error details: {result['error']}")

        return result
    
    def send(
        self,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[Union[str, List[Dict[str, Any]]]] = None,
        **kwargs
    ) -> Union[Dict[str, Any], Any]:
        """Send a request to the AI model.

        Args:
            system_prompt: The system prompt.
            user_prompt: The user prompt, either as a string or a list of message objects.
            **kwargs: Additional parameters for the API request.

        Returns:
            The API response.

        Raises:
            ClientError: If no model is selected or the request fails.
        """
        # Ensure we have a model selected
        if self._current_manager is None:
            # Try to use a default model if none is selected
            models = self._model_registry.get_all_models()
            if models:
                self._current_manager = ModelFactory.create_client_manager_for_model(models[0])
                # Use chat capability as default
                from flow_api.core.capabilities import Capability
                self._current_client = self._current_manager.get_client(Capability.CHAT_CONVERSATION)
            else:
                log_and_raise(logger, "No model selected. Use with_model() or with_capability() first.", ClientError, "debug")

        # If no specific client is set, use chat as default
        if self._current_client is None and self._current_manager is not None:
            from flow_api.core.capabilities import Capability
            self._current_client = self._current_manager.get_client(Capability.CHAT_CONVERSATION)

        # Process user_prompt to ensure it's in the correct format
        messages = validate_messages(user_prompt)

        # Build the request using the specialized client
        return self._send_with_specialized_client(system_prompt, messages, **kwargs)

    def _send_with_specialized_client(
        self,
        system_prompt: Optional[str],
        messages: List[Dict[str, Any]],
        **kwargs
    ) -> Dict[str, Any]:
        """Send a request using the specialized client.

        Args:
            system_prompt: The system prompt.
            messages: The user messages.
            **kwargs: Additional parameters for the API request.

        Returns:
            The API response.

        Raises:
            ClientError: If the request fails.
        """
        import json
        import requests
        from flow_api.adapters.outbound.auth.token_manager import TokenManager
        from flow_api.config import Config

        # Ensure we have a current client
        if self._current_client is None:
            log_and_raise(logger, "No specialized client available. Use with_model() or with_capability() first.", ClientError, "debug")

        # Prepare messages for the API
        api_messages = []
        if system_prompt:
            api_messages.append({"role": "system", "content": system_prompt})
        api_messages.extend(messages)

        # Build the request payload using the specialized client
        # For speech clients, don't pass messages
        if hasattr(self._current_client, 'requires_multipart') and self._current_client.requires_multipart():
            payload = self._current_client.build_request_payload(**kwargs)
        else:
            payload = self._current_client.build_request_payload(
                messages=api_messages,
                **kwargs
            )

        # Log payload (skip for multipart to avoid binary data issues)
        if hasattr(self._current_client, 'requires_multipart') and self._current_client.requires_multipart():
            logger.debug(f"Multipart payload prepared with keys: {list(payload.keys())}")
        else:
            logger.debug(f"Payload before sending: {json.dumps(payload, indent=2)}")

        # Get authentication token
        token_manager = TokenManager()
        token = token_manager.get_valid_token()

        # Get endpoint URL
        config = Config()
        endpoint_path = self._current_client.get_endpoint_path()
        url = f"{config.api_base_url}{endpoint_path}"

        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'FlowTenant': config.flow_tenant,
            'Authorization': f'Bearer {token}',
            'FlowAgent': self._current_client.model.name
        }

        # Make the API request
        try:
            logger.debug(f"Sending request to {url}")

            # Check if client requires multipart/form-data
            if hasattr(self._current_client, 'requires_multipart') and self._current_client.requires_multipart():
                # Remove Content-Type header for multipart (requests will set it automatically)
                if 'Content-Type' in headers:
                    del headers['Content-Type']

                # Separate files from regular form data
                files = {}
                data = {}
                for key, value in payload.items():
                    if isinstance(value, tuple) and len(value) == 3:
                        # This is a file (filename, content, content_type)
                        files[key] = value
                    else:
                        # This is regular form data
                        data[key] = value

                # Send multipart request
                response = requests.post(url, headers=headers, files=files, data=data)
                response.raise_for_status()
                result = response.json()

                # Log the response with truncated content for debugging
                from flow_api.utils.logging import truncate_response_content
                truncated_result = truncate_response_content(result, max_length=50)
                logger.debug(f"API response received: {json.dumps(truncated_result, indent=2)}")
            else:
                # Regular JSON request
                response = requests.post(url, headers=headers, data=json.dumps(payload))
                response.raise_for_status()
                result = response.json()

                # Log the response with truncated content for debugging
                from flow_api.utils.logging import truncate_response_content
                truncated_result = truncate_response_content(result, max_length=50)
                logger.debug(f"API response received: {json.dumps(truncated_result, indent=2)}")

            # Process the response using the appropriate response processor
            if self._response_processor:
                return self._response_processor.process_response(result)
            else:
                # Fallback to legacy processing if no processor available
                return self._legacy_process_response(result)

        except requests.exceptions.HTTPError as err:
            # Try to extract detailed error information from response
            error_details = f"HTTP error: {err}"
            try:
                if hasattr(err, 'response') and err.response is not None:
                    try:
                        error_response = err.response.json()
                        from flow_api.utils.logging import extract_error_message
                        detailed_message = extract_error_message(error_response)
                        error_details = f"HTTP error: {err.response.status_code} {err.response.reason} for url: {err.response.url} - {detailed_message}"
                        logger.error(f"Full error response: {error_response}")
                    except (ValueError, KeyError):
                        # If JSON parsing fails, use the response text
                        error_text = err.response.text if err.response.text else str(err)
                        error_details = f"HTTP error: {err.response.status_code} {err.response.reason} for url: {err.response.url} - {error_text}"
            except Exception:
                # If anything fails, fall back to original error
                pass
            log_and_raise(logger, error_details, ClientError, "error")
        except requests.exceptions.RequestException as err:
            log_and_raise(logger, f"Request error: {err}", ClientError, "error")
        except Exception as err:
            log_and_raise(logger, f"Unexpected error: {err}", ClientError, "error")

    def _legacy_process_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Legacy response processing for backward compatibility.

        This is a simplified fallback when no response processor is available.

        Args:
            result: Raw API response.

        Returns:
            Processed response in OpenAI format.
        """
        # For legacy compatibility, assume OpenAI format or return as-is
        if 'choices' in result:
            return result
        elif 'data' in result or 'combinedPhrases' in result or 'phrases' in result:
            # Image generation or speech-to-text format - return as-is
            return result
        else:
            # Try to create a basic OpenAI-compatible response
            content = ""
            if 'generation' in result:
                content = result.get('generation', '')
            elif 'content' in result:
                if isinstance(result['content'], list):
                    for item in result['content']:
                        if isinstance(item, dict) and 'text' in item:
                            content += item['text']
                else:
                    content = str(result['content'])

            model_name = self._current_client.model.name if self._current_client and self._current_client.model else "unknown"
            return {
                "choices": [{
                    "message": {
                        "role": "assistant",
                        "content": content
                    },
                    "finish_reason": "stop"
                }],
                "model": result.get("model", model_name),
                "usage": result.get("usage", {})
            }

    def get_answer(
        self,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[Union[str, List[Dict[str, Any]]]] = None,
        **kwargs
    ) -> str:
        """Get the answer from the AI model.

        This is a convenience method that extracts the answer content from the API response.

        Args:
            system_prompt: The system prompt.
            user_prompt: The user prompt, either as a string or a list of message objects.
            **kwargs: Additional parameters for the API request.

        Returns:
            The answer content from the API response.

        Raises:
            ClientError: If no model is selected or the request fails.
        """
        # Get the full response
        response = self.send(system_prompt, user_prompt, **kwargs)

        # Extract content from the response
        content = ""
        choices = response.get("choices", [])

        if not choices:
            log_and_raise(logger, "No choices found in API response", ClientError, "warning")

        for i, choice in enumerate(choices):
            message = choice.get("message", {})
            part_content = message.get("content", "")
            if not part_content:
                logger.warning(f"Choice {i} contains empty or invalid 'content': {message}")
                # Don't raise error immediately, continue with other choices
                continue
            content += part_content

        if not content:
            log_and_raise(logger, "API response contains no valid content", ClientError, "warning")

        return content

    def generate_embedding(
        self,
        text: str,
        model: Optional[str] = None,
        encoding_format: str = "float",
        **kwargs
    ) -> Dict[str, Any]:
        """Generate text embeddings.

        Args:
            text: The text to embed.
            model: The model to use. If None, uses current model or finds one with text-embedding capability.
            encoding_format: The encoding format for the embeddings.
            **kwargs: Additional parameters for the API request.

        Returns:
            The API response containing embeddings.

        Raises:
            ClientError: If no suitable model is found or the request fails.
        """
        # Set model if provided
        if model:
            self.with_model(model)
        elif self._current_manager is None:
            # Auto-select a model with text embedding capability
            from flow_api.core.capabilities import Capability
            self.with_capability(Capability.TEXT_EMBEDDING)

        # Ensure we have a manager
        if self._current_manager is None:
            log_and_raise(logger, "No model manager available for text embedding", ClientError, "debug")

        # Set the text embedding capability
        from flow_api.core.capabilities import Capability
        self._current_client = self._current_manager.get_client(Capability.TEXT_EMBEDDING)

        return self.send(input=text, encoding_format=encoding_format, **kwargs)

    def generate_image(
        self,
        prompt: str,
        model: Optional[str] = None,
        size: str = "1024x1024",
        quality: str = "standard",
        n: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate images from text prompts.

        Args:
            prompt: The text prompt for image generation.
            model: The model to use. If None, uses current model or finds one with image-generation capability.
            size: The size of the generated image.
            quality: The quality of the generated image.
            n: The number of images to generate.
            **kwargs: Additional parameters for the API request.

        Returns:
            The API response containing generated images.

        Raises:
            ClientError: If no suitable model is found or the request fails.
        """
        # Set model if provided
        if model:
            self.with_model(model)
        elif self._current_manager is None:
            # Auto-select a model with image generation capability
            from flow_api.core.capabilities import Capability
            self.with_capability(Capability.IMAGE_GENERATION)

        # Ensure we have a manager
        if self._current_manager is None:
            log_and_raise(logger, "No model manager available for image generation", ClientError, "debug")

        # Set the image generation capability
        from flow_api.core.capabilities import Capability
        self._current_client = self._current_manager.get_client(Capability.IMAGE_GENERATION)

        return self.send(prompt=prompt, size=size, quality=quality, n=n, **kwargs)

    def generate_transcript(
        self,
        audio_data: str,
        model: Optional[str] = None,
        language: str = "en",
        response_format: str = "json",
        temperature: float = 0.0,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate transcript from speech (speech-to-text)."

        Args:
            audio_data: The base64-encoded audio data.
            model: The model to use. If None, uses current model or finds one with speech-to-text capability.
            language: The language of the audio.
            response_format: The format of the response.
            temperature: The sampling temperature.
            **kwargs: Additional parameters for the API request.

        Returns:
            The API response containing transcription.

        Raises:
            ClientError: If no suitable model is found or the request fails.
        """
        # Set model if provided
        if model:
            self.with_model(model)
        elif self._current_manager is None:
            # Auto-select a model with speech-to-text capability
            from flow_api.core.capabilities import Capability
            self.with_capability(Capability.SPEECH_TO_TEXT)

        # Ensure we have a manager
        if self._current_manager is None:
            log_and_raise(logger, "No model manager available for speech-to-text", ClientError, "debug")

        # Set the speech-to-text capability
        from flow_api.core.capabilities import Capability
        self._current_client = self._current_manager.get_client(Capability.SPEECH_TO_TEXT)

        return self.send(file=audio_data, language=language, response_format=response_format, temperature=temperature, **kwargs)

    def get_stream_answer(
        self,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[Union[str, List[Dict[str, Any]]]] = None,
        **kwargs
    ):
        """Get a streaming answer from the AI model.

        This method uses the internal StreamingClient for better organization while
        maintaining a simple user interface.

        Args:
            system_prompt: The system prompt.
            user_prompt: The user prompt, either as a string or a list of message objects.
            **kwargs: Additional parameters for the API request.

        Yields:
            Content chunks from the streaming response.

        Raises:
            ClientError: If no model is selected or the request fails.

        Example:
            >>> client = FlowAPIClient("gpt-4o-mini")
            >>> for chunk in client.get_stream_answer("Tell me a story"):
            ...     print(chunk, end="", flush=True)
        """
        # Ensure we have a model selected
        if self._current_manager is None:
            log_and_raise(
                logger,
                "No model selected. Use with_model() or with_capability() first.",
                ClientError,
                "debug"
            )

        # Create internal streaming client
        from flow_api.adapters.inbound.streaming_client import StreamingClient
        streaming_client = StreamingClient()

        # Configure with current model
        current_model_name = self.get_current_model()
        if current_model_name:
            streaming_client.with_model(current_model_name)
        else:
            log_and_raise(
                logger,
                "No model available for streaming.",
                ClientError,
                "debug"
            )

        # Stream using the dedicated client
        try:
            for chunk in streaming_client.stream(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                **kwargs
            ):
                yield chunk
        except Exception as e:
            log_and_raise(logger, f"Streaming error: {e}", ClientError, "error")

    def get_reasoning_answer(
        self,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[Union[str, List[Dict[str, Any]]]] = None,
        **kwargs
    ) -> ReasoningResponse:
        """Get a response from reasoning models with separated thinking and final answer.

        This method is specifically designed for reasoning models like Deepseek and o1
        that provide both thinking process and final answer.

        Args:
            system_prompt: The system prompt.
            user_prompt: The user prompt, either as a string or a list of message objects.
            **kwargs: Additional parameters for the API request.

        Returns:
            ReasoningResponse object with convenient access methods:
            - .thinking: The reasoning/thinking process
            - .content: The final answer
            - .thinking_only(): Get only the thinking process
            - .answer_only(): Get only the final answer
            - .full_response: The complete API response
            - .full_content: The original unprocessed content

        Raises:
            ClientError: If no model is selected or the request fails.

        Example:
            >>> client = FlowAPIClient("deepseek-reasoning")
            >>> response = client.get_reasoning_answer("Solve: 15% of 240")
            >>> print(f"Thinking: {response.thinking}")
            >>> print(f"Answer: {response.content}")
            >>> # Or use convenience methods
            >>> thinking = response.thinking_only()
            >>> answer = response.answer_only()
        """
        # Get the full response first
        response = self.send(system_prompt, user_prompt, **kwargs)

        # Use response processor to extract reasoning content if available
        if self._response_processor and hasattr(self._response_processor, 'extract_reasoning_content'):
            reasoning_content = self._response_processor.extract_reasoning_content(response)
            reasoning_content['full_response'] = response
            return ReasoningResponse(reasoning_content)
        else:
            # Fallback for non-reasoning models
            content = ""
            choices = response.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")

            reasoning_data = {
                "thinking": "",
                "content": content,
                "full_response": response,
                "full_content": content
            }
            return ReasoningResponse(reasoning_data)

    def supports_reasoning(self) -> bool:
        """Check if the current model supports reasoning capabilities.

        Returns:
            True if the current model supports reasoning, False otherwise.
        """
        if self._response_processor and hasattr(self._response_processor, 'supports_reasoning'):
            return self._response_processor.supports_reasoning()
        return False



