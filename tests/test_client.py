"""
Tests for the main FlowAPIClient class.
"""

import pytest
from unittest.mock import Mock, patch
from typing import List

from flow_api import FlowAPIClient, Capability
from flow_api.exceptions.client_error import ClientError
from flow_api.core.models import ModelProvider


class TestClientInitialization:
    """Test client initialization and configuration."""
    
    def test_client_init_without_default_model(self, mock_env_vars, mock_models_data):
        """Test client initialization without default model."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'):
            client = FlowAPIClient()
            assert client._current_manager is None
            assert client._current_client is None
    
    def test_client_init_with_default_model(self, mock_env_vars, mock_models_data):
        """Test client initialization with default model."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_model') as mock_with_model:
            
            client = FlowAPIClient(default_model="gpt-4o-mini")
            mock_with_model.assert_called_once_with("gpt-4o-mini")


class TestModelSelection:
    """Test model selection methods."""
    
    def test_with_model_success(self, mock_env_vars, sample_ai_models):
        """Test successful model selection."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.adapters.inbound.model_factory.ModelFactory.create_client_manager') as mock_factory, \
             patch('flow_api.adapters.inbound.response_processors.processor_factory.ResponseProcessorFactory.create_processor') as mock_processor_factory:
            
            # Create a properly configured mock manager with model that has a valid provider
            mock_manager = Mock()
            mock_model = Mock()
            mock_model.name = "gpt-4o-mini"
            mock_model.provider = ModelProvider.AZURE_OPENAI  # Set a valid provider
            mock_manager.model = mock_model
            mock_factory.return_value = mock_manager
            
            # Mock the processor factory to return a mock processor
            mock_processor = Mock()
            mock_processor_factory.return_value = mock_processor
            
            client = FlowAPIClient()
            result = client.with_model("gpt-4o-mini")
            
            assert result is client  # Should return self for chaining
            assert client._current_manager is mock_manager
            assert client._current_client is None  # Should be set when capability is needed
            assert client._response_processor is mock_processor  # Should have a processor
            mock_factory.assert_called_once_with("gpt-4o-mini")
            mock_processor_factory.assert_called_once_with(mock_model)
    
    def test_with_model_nonexistent(self, mock_env_vars):
        """Test model selection with non-existent model."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.adapters.inbound.model_factory.ModelFactory.create_client_manager') as mock_factory:
            
            mock_factory.side_effect = ClientError("Model not found")
            
            client = FlowAPIClient()
            with pytest.raises(ClientError, match="Model not found"):
                client.with_model("nonexistent-model")
    
    def test_with_capability_success(self, mock_env_vars, sample_ai_models):
        """Test successful capability-based model selection."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.core.model_registry.ModelRegistry.get_models_by_capability') as mock_get_models, \
             patch('flow_api.adapters.inbound.model_factory.ModelFactory.create_client_manager_for_model') as mock_factory, \
             patch('flow_api.adapters.inbound.response_processors.processor_factory.ResponseProcessorFactory.create_processor') as mock_processor_factory:
            
            mock_get_models.return_value = sample_ai_models[:1]  # Return first model
            mock_manager = Mock()
            mock_client = Mock()
            mock_factory.return_value = mock_manager
            mock_manager.get_client.return_value = mock_client
            
            # Mock the processor factory
            mock_processor = Mock()
            mock_processor_factory.return_value = mock_processor
            
            client = FlowAPIClient()
            result = client.with_capability(Capability.CHAT_CONVERSATION)
            
            assert result is client
            assert client._current_manager is mock_manager
            assert client._current_client is mock_client
            mock_get_models.assert_called_once_with(Capability.CHAT_CONVERSATION)
    
    def test_with_capability_string(self, mock_env_vars, sample_ai_models):
        """Test capability selection with string parameter."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.core.model_registry.ModelRegistry.get_models_by_capability') as mock_get_models, \
             patch('flow_api.adapters.inbound.model_factory.ModelFactory.create_client_manager_for_model') as mock_factory, \
             patch('flow_api.adapters.inbound.response_processors.processor_factory.ResponseProcessorFactory.create_processor') as mock_processor_factory:
            
            mock_get_models.return_value = sample_ai_models[:1]
            mock_manager = Mock()
            mock_client = Mock()
            mock_factory.return_value = mock_manager
            mock_manager.get_client.return_value = mock_client
            
            # Mock the processor factory
            mock_processor = Mock()
            mock_processor_factory.return_value = mock_processor
            
            client = FlowAPIClient()
            result = client.with_capability("chat-conversation")
            
            assert result is client
            mock_get_models.assert_called_once_with(Capability.CHAT_CONVERSATION)
    
    def test_with_capability_invalid_string(self, mock_env_vars):
        """Test capability selection with invalid string."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'):
            client = FlowAPIClient()
            with pytest.raises(ClientError, match="Invalid capability"):
                client.with_capability("invalid-capability")
    
    def test_with_capability_no_models(self, mock_env_vars):
        """Test capability selection when no models have the capability."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.core.model_registry.ModelRegistry.get_models_by_capability') as mock_get_models:
            
            mock_get_models.return_value = []
            
            client = FlowAPIClient()
            with pytest.raises(ClientError, match="No model found with capability"):
                client.with_capability(Capability.CHAT_CONVERSATION)
    
    def test_with_capabilities_success(self, mock_env_vars, sample_ai_models):
        """Test successful multi-capability model selection."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.core.model_registry.ModelRegistry.get_models_by_capabilities') as mock_get_models, \
             patch('flow_api.adapters.inbound.model_factory.ModelFactory.create_client_manager_for_model') as mock_factory, \
             patch('flow_api.adapters.inbound.response_processors.processor_factory.ResponseProcessorFactory.create_processor') as mock_processor_factory:
            
            mock_get_models.return_value = sample_ai_models[:1]
            mock_manager = Mock()
            mock_client = Mock()
            mock_factory.return_value = mock_manager
            mock_manager.get_client.return_value = mock_client
            
            # Mock the processor factory
            mock_processor = Mock()
            mock_processor_factory.return_value = mock_processor
            
            client = FlowAPIClient()
            capabilities: List[Capability] = [Capability.CHAT_CONVERSATION, Capability.STREAMING]
            result = client.with_capabilities(capabilities) # type: ignore[arg-type]
            
            assert result is client
            assert client._current_manager is mock_manager
            assert client._current_client is mock_client
            mock_get_models.assert_called_once_with(capabilities)
    
    def test_with_capabilities_mixed_types(self, mock_env_vars, sample_ai_models):
        """Test multi-capability selection with mixed string and enum types."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.core.model_registry.ModelRegistry.get_models_by_capabilities') as mock_get_models, \
             patch('flow_api.adapters.inbound.model_factory.ModelFactory.create_client_manager_for_model') as mock_factory, \
             patch('flow_api.adapters.inbound.response_processors.processor_factory.ResponseProcessorFactory.create_processor') as mock_processor_factory:
            
            mock_get_models.return_value = sample_ai_models[:1]
            mock_manager = Mock()
            mock_client = Mock()
            mock_factory.return_value = mock_manager
            mock_manager.get_client.return_value = mock_client
            
            # Mock the processor factory
            mock_processor = Mock()
            mock_processor_factory.return_value = mock_processor
            
            client = FlowAPIClient()
            capabilities = ["chat-conversation", Capability.STREAMING]
            result = client.with_capabilities(capabilities)
            
            assert result is client
            expected_capabilities = [Capability.CHAT_CONVERSATION, Capability.STREAMING]
            mock_get_models.assert_called_once_with(expected_capabilities)


class TestClientInfo:
    """Test client information methods."""
    
    def test_get_current_model_with_manager(self, mock_env_vars):
        """Test getting current model when manager exists."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'):
            client = FlowAPIClient()
            mock_manager = Mock()
            mock_manager.model.name = "gpt-4o-mini"
            client._current_manager = mock_manager
            
            result = client.get_current_model()
            assert result == "gpt-4o-mini"
    
    def test_get_current_model_without_manager(self, mock_env_vars):
        """Test getting current model when no manager exists."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'):
            client = FlowAPIClient()
            result = client.get_current_model()
            assert result is None
    
    def test_list_models(self, mock_env_vars, sample_ai_models):
        """Test listing all available models."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.core.model_registry.ModelRegistry.get_all_models') as mock_get_all:
            
            mock_get_all.return_value = sample_ai_models
            
            client = FlowAPIClient()
            result = client.list_models()
            
            assert len(result) == len(sample_ai_models)
            assert all(isinstance(model, dict) for model in result)
            mock_get_all.assert_called_once()
    
    def test_list_capabilities(self, mock_env_vars):
        """Test listing all available capabilities."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.core.model_registry.ModelRegistry.get_available_capabilities') as mock_get_caps:
            
            mock_capabilities = {Capability.CHAT_CONVERSATION, Capability.TEXT_EMBEDDING}
            mock_get_caps.return_value = mock_capabilities
            
            client = FlowAPIClient()
            result = client.list_capabilities()
            
            assert len(result) == 2
            assert "chat-conversation" in result
            assert "text-embedding" in result
            mock_get_caps.assert_called_once()


class TestClientRequests:
    """Test client request methods."""
    
    def test_send_with_auto_model_selection(self, mock_env_vars, sample_ai_models, mock_api_responses, mock_auth_and_models):
        """Test send method with automatic model selection."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.core.model_registry.ModelRegistry.get_all_models') as mock_get_all, \
             patch('flow_api.adapters.inbound.model_factory.ModelFactory.create_client_manager_for_model') as mock_factory, \
             patch('flow_api.adapters.inbound.response_processors.processor_factory.ResponseProcessorFactory.create_processor') as mock_processor_factory:

            # Setup mocks
            mock_get_all.return_value = sample_ai_models
            mock_manager = Mock()
            mock_client = Mock()
            mock_factory.return_value = mock_manager
            mock_manager.get_client.return_value = mock_client

            # Mock the processor factory
            mock_processor = Mock()
            mock_processor.process_response.return_value = mock_api_responses['chat_completion']
            mock_processor_factory.return_value = mock_processor

            # Override the post mock for API calls (not token/models)
            api_response = Mock()
            api_response.status_code = 200
            api_response.json.return_value = mock_api_responses['chat_completion']
            api_response.raise_for_status.return_value = None

            def post_side_effect(url, **kwargs):
                if 'token' in url:
                    return mock_auth_and_models['token_response']
                elif 'models' in url or 'capabilities' in url:
                    return mock_auth_and_models['models_response']
                else:
                    return api_response

            mock_auth_and_models['post'].side_effect = post_side_effect

            # Mock client methods
            mock_client.build_request_payload.return_value = {"messages": [{"role": "user", "content": "test"}]}
            mock_client.get_endpoint_path.return_value = "/test/endpoint"
            mock_client.model.name = "gpt-4o-mini"
            mock_client.model.provider = ModelProvider.AZURE_OPENAI

            client = FlowAPIClient()
            result = client.send(user_prompt="test message")

            assert "choices" in result
            assert result["choices"][0]["message"]["content"] == "This is a test response from the AI model."
    
    def test_get_answer_success(self, mock_env_vars, sample_ai_models, mock_api_responses):
        """Test get_answer method success."""
        with patch.object(FlowAPIClient, 'send') as mock_send:
            mock_send.return_value = mock_api_responses['chat_completion']

            client = FlowAPIClient()
            result = client.get_answer(user_prompt="test message")

            assert result == "This is a test response from the AI model."
            mock_send.assert_called_once_with(None, "test message")
    
    def test_get_answer_no_choices(self, mock_env_vars, mock_auth_and_models):
        """Test get_answer when API returns no choices."""
        with patch.object(FlowAPIClient, 'send') as mock_send:
            mock_send.return_value = {"choices": []}

            client = FlowAPIClient()
            with pytest.raises(ClientError, match="No choices found in API response"):
                client.get_answer(user_prompt="test message")