"""
Tests for client capability-specific methods.
"""

import pytest
from unittest.mock import Mock, patch
import base64

from flow_api import FlowAPIClient, Capability
from flow_api.exceptions.client_error import ClientError


class TestTextEmbedding:
    """Test text embedding functionality."""
    
    def test_generate_embedding_with_model(self, mock_env_vars, sample_ai_models, mock_api_responses):
        """Test text embedding generation with specific model."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_model') as mock_with_model, \
             patch.object(FlowAPIClient, 'send') as mock_send:
            
            mock_send.return_value = mock_api_responses['text_embedding']
            
            client = FlowAPIClient()
            # Mock manager e client
            mock_manager = Mock()
            mock_client = Mock()
            mock_manager.get_client.return_value = mock_client
            client._current_manager = mock_manager
            client._current_client = mock_client
            
            result = client.generate_embedding(
                text="Test text for embedding",
                model="text-embedding-ada-002"
            )
            
            mock_with_model.assert_called_once_with("text-embedding-ada-002")
            mock_send.assert_called_once_with(
                input="Test text for embedding",
                encoding_format="float"
            )
            assert result == mock_api_responses['text_embedding']
    
    def test_generate_embedding_auto_select(self, mock_env_vars, sample_ai_models, mock_api_responses):
        """Test text embedding generation with auto model selection."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_capability') as mock_with_capability, \
             patch.object(FlowAPIClient, 'send') as mock_send:
            
            mock_send.return_value = mock_api_responses['text_embedding']
            
            client = FlowAPIClient()
            # Do not set _current_manager here!
            # Configurar o mock para simular o comportamento real
            mock_manager = Mock()
            mock_client = Mock()
            mock_manager.get_client.return_value = mock_client
            def with_capability_side_effect(cap):
                client._current_manager = mock_manager
                client._current_client = mock_client
                return client
            mock_with_capability.side_effect = with_capability_side_effect
            
            result = client.generate_embedding(text="Test text for embedding")
            
            mock_with_capability.assert_called_once_with(Capability.TEXT_EMBEDDING)
            mock_send.assert_called_once_with(
                input="Test text for embedding",
                encoding_format="float"
            )
            assert result == mock_api_responses['text_embedding']
    
    def test_generate_embedding_with_options(self, mock_env_vars, mock_api_responses):
        """Test text embedding generation with additional options."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_capability'), \
             patch.object(FlowAPIClient, 'send') as mock_send:
            
            mock_send.return_value = mock_api_responses['text_embedding']
            
            client = FlowAPIClient()
            # Mock manager e client
            mock_manager = Mock()
            mock_client = Mock()
            mock_manager.get_client.return_value = mock_client
            client._current_manager = mock_manager
            client._current_client = mock_client
            
            result = client.generate_embedding(
                text="Test text",
                encoding_format="base64",
                dimensions=512,
                user="test_user"
            )
            
            mock_send.assert_called_once_with(
                input="Test text",
                encoding_format="base64",
                dimensions=512,
                user="test_user"
            )
    
    def test_generate_embedding_no_manager(self, mock_env_vars):
        """Test text embedding generation when no manager is available."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_capability') as mock_with_capability:
            
            mock_with_capability.side_effect = ClientError("No model manager available")
            
            client = FlowAPIClient()
            with pytest.raises(ClientError, match="No model manager available"):
                client.generate_embedding(text="Test text")


class TestImageGeneration:
    """Test image generation functionality."""
    
    def test_generate_image_with_model(self, mock_env_vars, mock_api_responses):
        """Test image generation with specific model."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_model') as mock_with_model, \
             patch.object(FlowAPIClient, 'send') as mock_send:
            
            mock_send.return_value = mock_api_responses['image_generation']
            
            client = FlowAPIClient()
            # Mock manager e client
            mock_manager = Mock()
            mock_client = Mock()
            mock_manager.get_client.return_value = mock_client
            client._current_manager = mock_manager
            client._current_client = mock_client
            
            result = client.generate_image(
                prompt="A futuristic cityscape",
                model="dall-e-3"
            )
            
            mock_with_model.assert_called_once_with("dall-e-3")
            mock_send.assert_called_once_with(
                prompt="A futuristic cityscape",
                size="1024x1024",
                quality="standard",
                n=1
            )
            assert result == mock_api_responses['image_generation']
    
    def test_generate_image_auto_select(self, mock_env_vars, mock_api_responses):
        """Test image generation with auto model selection."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_capability') as mock_with_capability, \
             patch.object(FlowAPIClient, 'send') as mock_send:
            
            mock_send.return_value = mock_api_responses['image_generation']
            
            client = FlowAPIClient()
            # Do not set _current_manager here!
            mock_manager = Mock()
            mock_client = Mock()
            mock_manager.get_client.return_value = mock_client
            def with_capability_side_effect(cap):
                client._current_manager = mock_manager
                client._current_client = mock_client
                return client
            mock_with_capability.side_effect = with_capability_side_effect
            
            result = client.generate_image(prompt="A futuristic cityscape")
            
            mock_with_capability.assert_called_once_with(Capability.IMAGE_GENERATION)
            mock_send.assert_called_once_with(
                prompt="A futuristic cityscape",
                size="1024x1024",
                quality="standard",
                n=1
            )
    
    def test_generate_image_with_options(self, mock_env_vars, mock_api_responses):
        """Test image generation with additional options."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_capability'), \
             patch.object(FlowAPIClient, 'send') as mock_send:
            
            mock_send.return_value = mock_api_responses['image_generation']
            
            client = FlowAPIClient()
            # Mock manager e client
            mock_manager = Mock()
            mock_client = Mock()
            mock_manager.get_client.return_value = mock_client
            client._current_manager = mock_manager
            client._current_client = mock_client
            
            result = client.generate_image(
                prompt="A futuristic cityscape",
                size="512x512",
                quality="hd",
                n=2,
                style="vivid",
                response_format="b64_json"
            )
            
            mock_send.assert_called_once_with(
                prompt="A futuristic cityscape",
                size="512x512",
                quality="hd",
                n=2,
                style="vivid",
                response_format="b64_json"
            )
    
    def test_generate_image_no_manager(self, mock_env_vars):
        """Test image generation when no manager is available."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_capability') as mock_with_capability:
            
            mock_with_capability.side_effect = ClientError("No model manager available")
            
            client = FlowAPIClient()
            with pytest.raises(ClientError, match="No model manager available"):
                client.generate_image(prompt="Test prompt")


class TestSpeechToText:
    """Test speech-to-text functionality."""
    
    def test_generate_transcript_with_model(self, mock_env_vars, mock_api_responses):
        """Test speech transcription with specific model."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_model') as mock_with_model, \
             patch.object(FlowAPIClient, 'send') as mock_send:
            
            mock_send.return_value = mock_api_responses['speech_to_text']
            
            # Create test audio data
            test_audio = b"fake audio data"
            audio_base64 = base64.b64encode(test_audio).decode('utf-8')
            
            client = FlowAPIClient()
            # Mock manager e client
            mock_manager = Mock()
            mock_client = Mock()
            mock_manager.get_client.return_value = mock_client
            client._current_manager = mock_manager
            client._current_client = mock_client
            
            result = client.generate_transcript(
                audio_data=audio_base64,
                model="whisper"
            )
            
            mock_with_model.assert_called_once_with("whisper")
            mock_send.assert_called_once_with(
                file=audio_base64,
                language="en",
                response_format="json",
                temperature=0.0
            )
            assert result == mock_api_responses['speech_to_text']
    
    def test_generate_transcript_auto_select(self, mock_env_vars, mock_api_responses):
        """Test speech transcription with auto model selection."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_capability') as mock_with_capability, \
             patch.object(FlowAPIClient, 'send') as mock_send:
            
            mock_send.return_value = mock_api_responses['speech_to_text']
            
            test_audio = b"fake audio data"
            audio_base64 = base64.b64encode(test_audio).decode('utf-8')
            
            client = FlowAPIClient()
            # Do not set _current_manager here!
            mock_manager = Mock()
            mock_client = Mock()
            mock_manager.get_client.return_value = mock_client
            def with_capability_side_effect(cap):
                client._current_manager = mock_manager
                client._current_client = mock_client
                return client
            mock_with_capability.side_effect = with_capability_side_effect
            
            result = client.generate_transcript(audio_data=audio_base64)
            
            mock_with_capability.assert_called_once_with(Capability.SPEECH_TO_TEXT)
            mock_send.assert_called_once_with(
                file=audio_base64,
                language="en",
                response_format="json",
                temperature=0.0
            )
            assert result == mock_api_responses['speech_to_text']
    
    def test_generate_transcript_with_options(self, mock_env_vars, mock_api_responses):
        """Test speech transcription with additional options."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_capability'), \
             patch.object(FlowAPIClient, 'send') as mock_send:
            
            mock_send.return_value = mock_api_responses['speech_to_text']
            
            test_audio = b"fake audio data"
            audio_base64 = base64.b64encode(test_audio).decode('utf-8')
            
            client = FlowAPIClient()
            # Mock manager e client
            mock_manager = Mock()
            mock_client = Mock()
            mock_manager.get_client.return_value = mock_client
            client._current_manager = mock_manager
            client._current_client = mock_client
            
            result = client.generate_transcript(
                audio_data=audio_base64,
                language="pt",
                response_format="verbose_json",
                temperature=0.2,
                timestamp_granularities=["word"]
            )
            
            mock_send.assert_called_once_with(
                file=audio_base64,
                language="pt",
                response_format="verbose_json",
                temperature=0.2,
                timestamp_granularities=["word"]
            )
    
    def test_generate_transcript_no_manager(self, mock_env_vars):
        """Test speech transcription when no manager is available."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch.object(FlowAPIClient, 'with_capability') as mock_with_capability:
            
            mock_with_capability.side_effect = ClientError("No model manager available")
            
            client = FlowAPIClient()
            with pytest.raises(ClientError, match="No model manager available"):
                client.generate_transcript(audio_data="fake_audio_data")


class TestStreamingFunctionality:
    """Test streaming functionality."""
    
    def test_get_stream_answer_success(self, mock_env_vars, sample_ai_models, mock_api_responses):
        """Test successful streaming request."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.core.model_registry.ModelRegistry.get_all_models') as mock_get_all, \
             patch('flow_api.core.model_registry.ModelRegistry.get_model') as mock_get_model, \
             patch('flow_api.adapters.inbound.model_factory.ModelFactory.create_client_manager_for_model') as mock_factory, \
             patch('requests.post') as mock_post:
            
            # Setup mocks
            mock_get_all.return_value = sample_ai_models
            mock_get_model.return_value = sample_ai_models[0]  # Return first model
            mock_manager = Mock()
            mock_client = Mock()
            mock_factory.return_value = mock_manager
            mock_manager.get_client.return_value = mock_client
            
            # Mock streaming response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.raise_for_status.return_value = None
            mock_response.iter_lines.return_value = [
                b'data: {"choices":[{"delta":{"content":"Hello"}}]}',
                b'data: {"choices":[{"delta":{"content":" world"}}]}',
                b'data: [DONE]'
            ]
            mock_post.return_value = mock_response
            
            # Mock client methods
            mock_client.build_request_payload.return_value = {
                "messages": [{"role": "user", "content": "test"}],
                "stream": True
            }
            mock_client.get_endpoint_path.return_value = "/test/endpoint"
            mock_client.model.name = "gpt-4o-mini"
            
            client = FlowAPIClient()
            client.with_model("gpt-4o-mini")  # Select a model first
            chunks = list(client.get_stream_answer(user_prompt="test message"))
            
            assert chunks == ["Hello", " world"]
    
    def test_get_stream_answer_no_model(self, mock_env_vars):
        """Test streaming when no model is available."""
        with patch('flow_api.core.model_registry.ModelRegistry._load_models'), \
             patch('flow_api.core.model_registry.ModelRegistry.get_all_models') as mock_get_all:
            
            mock_get_all.return_value = []
            
            client = FlowAPIClient()
            with pytest.raises(ClientError, match="No model selected"):
                list(client.get_stream_answer(user_prompt="test message"))
