# Changelog

All notable changes to the Flow API Python Library will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.2.0] - 2025-06-25

### Added

- Added optional format of response for reasoning models (DeepSeek and o1)
- New examples for reasoning and streaming
- `extract_error_message()` function in `flow_api.utils` to extract complete error messages from API responses

### Fixed

- Correct handling of system prompt for Claude models
- `get_stream_answer()` method in `FlowAPIClient` now correctly handle Amazon Bedrock

### Changed

- Refactored `client.py` to improve architecture and reduce complexity

## [1.1.0] - 2025-06-24

### Added

- `check_connection()` method in `FlowAPIClient` to verify API connectivity and authentication before making requests
- `token_source` field in the response of `check_connection()` to indicate whether the token was obtained from cache or newly generated

## [1.0.0] - 2025-06-20

### Added

- Initial stable release
- Complete Python library for Flow API integration
- Support all AI providers available by Flow
- Comprehensive test suite with unit and integration tests
- Detailed documentation and usage examples
- Model availability tracking
- Capability-based model selection
- Automatic token management and caching
- Support for all AI capabilities available:
  - Chat conversations with streaming
  - Image generation and recognition
  - Text embeddings
  - Speech to text
  - System instructions

## Model Availability History

### 2025-01-16 - Current Models

#### Azure OpenAI

- **gpt-4o** - Chat, Image Recognition, Streaming, System Instructions (128K tokens)
- **gpt-4o-mini** - Chat, Image Recognition, Streaming, System Instructions (128K tokens)
- **gpt-4.1** - Chat, Image Recognition, Streaming, System Instructions (1M tokens)
- **o1** - Chat, Image Recognition, Streaming (200K tokens)
- **o1-mini** - Chat, Streaming (128K tokens)
- **o3-mini** - Chat, Streaming, System Instructions (200K tokens)
- **text-embedding-ada-002** - Text Embedding, Streaming (8K tokens)
- **text-embedding-3-small** - Text Embedding, Streaming (8K tokens)
- **dall-e-3** - Image Generation (1K tokens)

#### Azure Foundry

- **DeepSeek-R1** - Chat, Streaming (128K tokens)

#### Google Gemini

- **gemini-2.5-pro** - Chat, Image Recognition, Streaming, System Instructions (1M tokens)
- **gemini-2.0-flash** - Chat, Image Recognition, Streaming, System Instructions (1M tokens)
- **textembedding-gecko@003** - Text Embedding, Streaming (8K tokens)

#### Amazon Bedrock

- **anthropic.claude-37-sonnet** - Chat, Image Recognition, Streaming, System Instructions (200K tokens)
- **meta.llama3-70b-instruct** - Chat, Streaming, System Instructions (8K tokens)
- **amazon.nova-lite** - Chat, Image Recognition, Streaming (300K tokens)
- **amazon.nova-micro** - Chat, Streaming (128K tokens)
- **amazon.nova-pro** - Chat, Image Recognition, Streaming (300K tokens)
- **amazon.titan-embed-text-v2** - Text Embedding (8K tokens)

#### Azure AI Speech

- **whisper** - Speech to Text
