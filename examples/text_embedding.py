#!/usr/bin/env python3
"""
Text embedding example with flow_api.

This example shows how to generate text embeddings for semantic search and similarity.
"""

import os
import sys
from flow_api import FlowAPIClient

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def main():
    """Simple text embedding example."""
    print("=== Flow API - Text Embedding Example ===\n")
    
    # Initialize client
    client = FlowAPIClient()
    
    # Find text embedding models
    models = client.list_models()
    embedding_models = [m for m in models if 'text-embedding' in m.get('capabilities', [])]
    
    if not embedding_models:
        print("❌ No text embedding models available")
        return
    
    # Use first available embedding model
    model_name = embedding_models[0]['name']
    client.with_model(model_name)

    # Set the text embedding capability
    from flow_api.core.capabilities import Capability
    client.with_capability(Capability.TEXT_EMBEDDING)
    
    print(f"🤖 Using model: {model_name}")

    # Show model details
    model_info = next((m for m in embedding_models if m['name'] == model_name), {})
    provider = model_info.get('provider', 'unknown')
    print(f"   Provider: {provider}")

    # Sample texts to embed
    texts = [
        "The quick brown fox jumps over the lazy dog",
        "A fast brown fox leaps over a sleepy dog",
        "Python is a programming language",
        "Machine learning is a subset of artificial intelligence"
    ]
    
    print("📝 Generating embeddings for sample texts:")
    for i, text in enumerate(texts, 1):
        print(f"  {i}. {text}")
    
    try:
        embeddings = []
        
        for i, text in enumerate(texts):
            print(f"\n🔄 Processing text {i+1}...")
            
            response = client.send(
                input=text,
                encoding_format="float"
            )
            
            # Extract embedding vector from response
            if 'data' in response and len(response['data']) > 0:
                embedding = response['data'][0]['embedding']
                embeddings.append(embedding)

                print(f"✅ Embedding generated (dimension: {len(embedding)})")
                print(f"   First 5 values: {embedding[:5]}")

                # Show usage information if available
                if 'usage' in response:
                    usage = response['usage']
                    tokens = usage.get('prompt_tokens', 0)
                    print(f"   Tokens used: {tokens}")
            else:
                print(f"❌ No embedding data in response")
        
        # Calculate similarity between first two texts (if we have embeddings)
        if len(embeddings) >= 2:
            print(f"\n📊 Calculating similarity between texts 1 and 2...")
            
            # Simple cosine similarity calculation
            import math
            
            def cosine_similarity(vec1, vec2):
                dot_product = sum(a * b for a, b in zip(vec1, vec2))
                magnitude1 = math.sqrt(sum(a * a for a in vec1))
                magnitude2 = math.sqrt(sum(a * a for a in vec2))
                return dot_product / (magnitude1 * magnitude2)
            
            similarity = cosine_similarity(embeddings[0], embeddings[1])
            print(f"   Cosine similarity: {similarity:.4f}")
            
            # Compare with texts 1 and 3
            if len(embeddings) >= 3:
                similarity_1_3 = cosine_similarity(embeddings[0], embeddings[2])
                print(f"   Similarity between texts 1 and 3: {similarity_1_3:.4f}")
                
                print(f"\n💡 Text 1 and 2 are more similar ({similarity:.4f}) than text 1 and 3 ({similarity_1_3:.4f})")
        
        print(f"\n✅ Embedding generation completed!")
        
    except Exception as e:
        print(f"❌ Error generating embeddings: {e}")
    
    print("\n💡 Use cases for embeddings:")
    print("- Semantic search and document retrieval")
    print("- Text similarity and clustering")
    print("- Recommendation systems")
    print("- Question answering systems")
    print("- Content classification")


if __name__ == "__main__":
    main()
