"""
Examples demonstrating streaming functionality with FlowAPIClient.

This script shows how to use streaming for real-time responses from AI models.
"""

from flow_api import FlowAPIClient


def example_basic_streaming():
    """Demonstrate basic streaming functionality."""
    print("=== Basic Streaming Examples ===\n")

    # Initialize client with model
    client = FlowAPIClient("gpt-4o-mini")

    print(f"✅ Using model: {client.get_current_model()}")

    # Stream a simple response
    print("\n1. Simple streaming:")
    print("Response: ", end="", flush=True)
    try:
        for chunk in client.get_stream_answer(user_prompt="Count from 1 to 5"):
            print(chunk, end="", flush=True)
        print("\n")

    except Exception as e:
        print(f"Error: {e}")


def example_openai_streaming():
    """Demonstrate streaming with OpenAI models."""
    print("\n=== OpenAI Streaming ===\n")

    client = FlowAPIClient("gpt-4o-mini")

    print("Streaming with OpenAI model:")
    try:
        print("Response: ", end="", flush=True)
        for chunk in client.get_stream_answer(user_prompt="Tell me a short joke"):
            print(chunk, end="", flush=True)
        print("\n")

    except Exception as e:
        print(f"Error: {e}")


def example_bedrock_streaming():
    """Demonstrate streaming with Amazon Bedrock models (improved parsing)."""
    print("\n=== Amazon Bedrock Streaming ===\n")

    client = FlowAPIClient("anthropic.claude-37-sonnet")

    print("Streaming with Amazon Bedrock model:")
    try:
        print("Response: ", end="", flush=True)
        for chunk in client.get_stream_answer(user_prompt="Explain AI in one sentence"):
            print(chunk, end="", flush=True)
        print("\n")

    except Exception as e:
        print(f"Error: {e}")


def example_streaming_parameters():
    """Demonstrate streaming with different parameters."""
    print("\n=== Streaming with Parameters ===\n")

    client = FlowAPIClient("gpt-4o-mini")

    print("Streaming with temperature=0.8:")
    print("Response: ", end="", flush=True)
    try:
        for chunk in client.get_stream_answer(
            user_prompt="Write a creative story opening",
            temperature=0.8,
            max_tokens=100
        ):
            print(chunk, end="", flush=True)
        print("\n")

    except Exception as e:
        print(f"Error: {e}")



def main():
    """Run all streaming examples."""
    print("Flow API - Streaming Examples")
    print("=" * 35)
    
    # Check connection
    client = FlowAPIClient()
    connection_result = client.check_connection()
    
    if connection_result['status'] == 'connected':
        print(f"✅ FlowAPI connected: {connection_result['message']}")
    else:
        print(f"❌ Connection check failed: {connection_result['message']}")
        print("🔧 Please check your configuration and try again.")
        return
    
    try:
        # Run examples
        example_basic_streaming()
        example_openai_streaming()
        example_bedrock_streaming()
        example_streaming_parameters()
        
        print("\n✅ All streaming examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("Make sure you have valid credentials and network access.")


if __name__ == "__main__":
    main()
