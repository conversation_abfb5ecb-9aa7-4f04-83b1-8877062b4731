#!/usr/bin/env python3
"""
Speech to text example with flow_api.

This example shows how to transcribe audio files to text.
"""

import os
import sys
import base64
import requests
from flow_api import FlowAPIClient

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def load_audio_file(file_path):
    """Load an audio file and return its binary data."""
    try:
        with open(file_path, "rb") as audio_file:
            return audio_file.read()
    except Exception as e:
        print(f"❌ Error loading audio file: {e}")
        return None


def download_audio_from_url(url):
    """Download audio file from URL and return its binary data."""
    try:
        print(f"🌐 Downloading audio from: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        print(f"✅ Downloaded {len(response.content)} bytes")
        return response.content
    except Exception as e:
        print(f"❌ Error downloading audio: {e}")
        return None


def main():
    """Simple speech to text example."""
    print("=== Flow API - Speech to Text Example ===\n")
    
    # Initialize client
    client = FlowAPIClient()
    
    # Find speech to text models
    models = client.list_models()
    speech_models = [m for m in models if 'speech-to-text' in m.get('capabilities', [])]
    
    if not speech_models:
        print("❌ No speech-to-text models available")
        return
    
    # Use first available speech model
    model_name = speech_models[0]['name']
    client.with_model(model_name)

    # Set the speech-to-text capability
    from flow_api.core.capabilities import Capability
    client.with_capability(Capability.SPEECH_TO_TEXT)
    
    print(f"🤖 Using model: {model_name}")

    # Show model details
    model_info = next((m for m in speech_models if m['name'] == model_name), {})
    provider = model_info.get('provider', 'unknown')
    print(f"   Provider: {provider}")

    # Offer different audio source options
    print("\n🎤 Audio source options:")
    print("1. Use demo audio from internet (recommended)")
    print("2. Local audio file")

    choice = input("Choose option (1-2, default 1): ").strip() or "1"

    if choice == "1":
        # Use a sample audio file from the internet
        # This is a short English speech sample
        audio_url = "https://cdn.pixabay.com/download/audio/2022/03/14/audio_76d9680154.mp3?filename=microphone-test-68625.mp3"
        print(f"📁 Using demo audio from internet")
        audio_data = download_audio_from_url(audio_url)
        if not audio_data:
            print("❌ Failed to download demo audio. Please try option 2 with a local file.")
            return
    elif choice == "2":
        audio_file_path = input("🎤 Enter path to audio file: ").strip()
        if audio_file_path and os.path.exists(audio_file_path):
            print(f"📁 Loading audio file: {audio_file_path}")
            audio_data = load_audio_file(audio_file_path)
            if not audio_data:
                return
        else:
            print("❌ File not found. Please provide a valid audio file path.")
            return
    else:
        print("❌ Invalid option. Please choose 1 or 2.")
        return
    
    try:
        print("🔄 Transcribing audio...")
        
        # Convert audio to base64 for API
        audio_base64 = base64.b64encode(audio_data).decode('utf-8')
        
        response = client.send(
            file=audio_base64,
            language="en",
            response_format="json",
            temperature=0.0
        )
        
        print("✅ Transcription completed!")
        print(f"📥 Response keys: {list(response.keys())}")

        # Extract transcription from response
        transcription = None
        if 'combinedPhrases' in response and response['combinedPhrases']:
            # Azure AI Speech format
            transcription = response['combinedPhrases'][0].get('text', '')
            print(f"📝 Transcription: {transcription}")
        elif 'text' in response:
            transcription = response['text']
            print(f"📝 Transcription: {transcription}")
        elif 'data' in response and isinstance(response['data'], dict) and 'text' in response['data']:
            transcription = response['data']['text']
            print(f"📝 Transcription: {transcription}")
        elif 'choices' in response and response['choices']:
            # Some providers might return in choices format
            choice = response['choices'][0]
            if 'message' in choice and 'content' in choice['message']:
                transcription = choice['message']['content']
                print(f"� Transcription: {transcription}")
        else:
            print(f"�📥 Full response: {response}")

        # Show additional information if available
        if 'durationMilliseconds' in response:
            duration_ms = response['durationMilliseconds']
            duration_sec = duration_ms / 1000
            print(f"⏱️ Audio duration: {duration_sec:.1f} seconds ({duration_ms} ms)")

        if 'phrases' in response:
            phrases = response['phrases']
            print(f"🗣️ Number of phrases: {len(phrases)}")

            # Show speaker information if available
            speakers = set()
            for phrase in phrases:
                if 'speaker' in phrase:
                    speakers.add(phrase['speaker'])
            if speakers:
                print(f"👥 Speakers detected: {sorted(speakers)}")

            # Show confidence if available
            confidences = [p.get('confidence', 0) for p in phrases if 'confidence' in p]
            if confidences:
                avg_confidence = sum(confidences) / len(confidences)
                print(f"🎯 Average confidence: {avg_confidence:.2%}")

        if 'language' in response:
            print(f"🌐 Detected language: {response['language']}")

        # Show usage information if available
        if 'usage' in response:
            usage = response['usage']
            print(f"💰 Usage: {usage}")

        if transcription:
            print(f"📊 Transcription length: {len(transcription)} characters")
        else:
            print("⚠️ No transcription text found in response")
        
    except Exception as e:
        print(f"❌ Error transcribing audio: {e}")
    
    print("\n💡 Tips:")
    print("- Supported formats: MP3, MP4, MPEG, MPGA, M4A, WAV, WEBM")
    print("- Maximum file size varies by provider (usually 25MB)")
    print("- For better accuracy, use clear audio with minimal background noise")
    print("- You can specify language codes like 'en', 'es', 'fr', etc.")
    print("- Response formats: 'json', 'text', 'srt', 'verbose_json', 'vtt'")


if __name__ == "__main__":
    main()
