#!/usr/bin/env python3
"""
List all available options in flow_api.

This example shows how to list models, capabilities, and providers.
"""

import os
import sys
from flow_api import FlowAPIClient
from flow_api.core.capabilities import Capability

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def main():
    """List all available options."""
    print("=== Flow API - Available Options ===\n")

    # Initialize client
    client = FlowAPIClient()
    
    # 1. List all models
    print("📋 Available Models:")
    try:
        models = client.list_models()
        for model in models:
            name = model.get('name', 'unknown')
            provider = model.get('provider', 'unknown')
            capabilities = model.get('capabilities', [])
            print(f"  • {name} ({provider})")
            print(f"    Capabilities: {', '.join(capabilities)}")
            print()
    except Exception as e:
        print(f"❌ Error listing models: {e}")
        return

    # 2. List all capabilities
    print("\n🔧 Available Capabilities:")
    capabilities = [
        Capability.CHAT_CONVERSATION,
        Capability.TEXT_EMBEDDING,
        Capability.IMAGE_GENERATION,
        Capability.IMAGE_RECOGNITION,
        Capability.SPEECH_TO_TEXT,
        Capability.STREAMING,
        Capability.SYSTEM_INSTRUCTION
    ]
    
    for cap in capabilities:
        print(f"  • {cap.value}")

    # 3. List models by capability
    print("\n📊 Models by Capability:")
    
    for capability in capabilities:
        print(f"\n{capability.value.upper()}:")
        capability_models = [m for m in models if capability.value in m.get('capabilities', [])]
        if capability_models:
            for model in capability_models:
                print(f"  • {model['name']} ({model.get('provider', 'unknown')})")
        else:
            print("  No models available")

    # 4. List providers
    print("\n🏢 Available Providers:")
    providers = set(model.get('provider', 'unknown') for model in models)
    for provider in sorted(providers):
        provider_models = [m for m in models if m.get('provider') == provider]
        print(f"  • {provider} ({len(provider_models)} models)")

    print(f"\n✅ Total: {len(models)} models across {len(providers)} providers")


if __name__ == "__main__":
    main()
