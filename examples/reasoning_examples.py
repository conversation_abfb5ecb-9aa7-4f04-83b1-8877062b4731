"""
Examples demonstrating reasoning models functionality.

This script shows how to use reasoning models like Deepseek and o1 that provide
separated thinking process and final answers.
"""

from flow_api import FlowAPIClient


def example_basic_reasoning():
    """Demonstrate basic reasoning model capabilities."""
    print("=== Basic Reasoning Examples ===\n")
    
    # Initialize client with a reasoning model
    client = FlowAPIClient("DeepSeek-R1")

    # Check if current model supports reasoning
    if client.supports_reasoning():
        print("✅ Current model supports reasoning capabilities")
    else:
        print("❌ Current model does not support reasoning")
        print("Switching to a regular model for demonstration...")
        client = FlowAPIClient("gpt-4o-mini")
    
    prompt = "Solve this step by step: What is 15% of 240?"
    
    # Example 1: Get complete reasoning response
    print("\n1. Complete reasoning response:")
    try:
        reasoning_response = client.get_reasoning_answer(user_prompt=prompt)
        
        print(f"=== Thinking process: {reasoning_response.thinking}")
        print(f"=== Final answer: {reasoning_response.content}")
        print(f"=== Full response from API: {reasoning_response.full_response}")
        print(f"=== Has thinking: {reasoning_response.has_thinking()}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Example 2: Get only the thinking process
    print("\n2. Only thinking process:")
    try:
        reasoning_response = client.get_reasoning_answer(user_prompt=prompt)
        thinking = reasoning_response.thinking_only()
        print(f"Thinking: {thinking}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Example 3: Get only the final answer
    print("\n3. Only final answer:")
    try:
        reasoning_response = client.get_reasoning_answer(user_prompt=prompt)
        answer = reasoning_response.answer_only()
        print(f"Answer: {answer}")
        
    except Exception as e:
        print(f"Error: {e}")


def example_reasoning_with_streaming():
    """Demonstrate reasoning models with streaming (combined functionality)."""
    print("\n=== Reasoning + Streaming ===\n")

    # Use reasoning model with streaming
    client = FlowAPIClient("DeepSeek-R1")

    print("Streaming reasoning response:")
    try:
        print("Response: ", end="", flush=True)
        for chunk in client.get_stream_answer(
            user_prompt="Think through this problem: If a train travels 60 mph for 2.5 hours, how far does it go?"
        ):
            print(chunk, end="", flush=True)
        print("\n")

        print("\nNote: In streaming mode, thinking and answer are mixed together.")
        print("For separated thinking/answer, use the regular reasoning client:")

        # Show separated reasoning
        print("\nSeparated reasoning response:")
        response = client.get_reasoning_answer(
            user_prompt="Think through this problem: If a train travels 60 mph for 2.5 hours, how far does it go?"
        )
        if response.has_thinking():
            print(f"Thinking: {response.thinking}")
        print(f"Answer: {response.content}")

    except Exception as e:
        print(f"Error: {e}")


def example_reasoning_comparison():
    """Compare reasoning vs regular models."""
    print("\n=== Reasoning vs Regular Models ===\n")
    
    problem = "Explain why 0.999... equals 1"
    
    print("Regular model response:")
    try:
        regular_client = FlowAPIClient("gpt-4o-mini")
        regular_response = regular_client.send(user_prompt=problem)
        content = regular_response["choices"][0]["message"]["content"]
        print(f"Answer: {content}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    print("\nReasoning model response:")
    try:
        reasoning_client = FlowAPIClient("o1")
        reasoning_response = reasoning_client.get_reasoning_answer(user_prompt=problem)
        
        if reasoning_response.has_thinking():
            print(f"Thinking: {reasoning_response.thinking}")
        print(f"Answer: {reasoning_response.content}")
        
    except Exception as e:
        print(f"Error: {e}")



def main():
    """Run all reasoning examples."""
    print("Flow API - Reasoning Models Examples")
    print("=" * 40)
    
    # Check connection
    client = FlowAPIClient()
    connection_result = client.check_connection()
    
    if connection_result['status'] == 'connected':
        print(f"✅ FlowAPI connected: {connection_result['message']}")
    else:
        print(f"❌ Connection check failed: {connection_result['message']}")
        print("🔧 Please check your configuration and try again.")
        return
    
    try:
        # Run examples
        example_basic_reasoning()
        example_reasoning_with_streaming()
        example_reasoning_comparison()
        
        print("\n✅ All reasoning examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("Make sure you have valid credentials and network access.")


if __name__ == "__main__":
    main()
